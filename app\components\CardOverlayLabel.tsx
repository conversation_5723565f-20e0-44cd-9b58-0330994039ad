import Link from 'next/link'
import { cn } from '@/lib/utils'

const CardOverlayLabel = ({
  cardOverlayLabel,
  link,
}: {
  cardOverlayLabel: string
  link?: string
}) => {
  return (
    <p className={cn('min-h-20 flex flex-1', !link && 'px-16 items-center')}>
      {link ? (
        <Link href={link} className="flex flex-1 items-center px-16">
          {cardOverlayLabel}
        </Link>
      ) : (
        cardOverlayLabel
      )}
    </p>
  )
}

export default CardOverlayLabel
