import { ReactNode } from 'react'

export type Feedback = {
  id: string
  text: string
  note?: string
  client: {
    name?: string
    title?: string
    company?: string
    website?: string
  }
  source: string
  project: {
    title: string
    term: {
      from: number
      to: number
    }
  }
  featured?: boolean
  order: number
}

export type Project = {
  id: string
  title: string
  description: string
  order: number
  website?: string
  thumbnail?: string
  slug?: string
}

export type Service = {
  id: string
  title: string
  description: string
  order: number
  thumbnail?: string
  slug?: string
}

export type Page = {
  slug: string
  title: string
  description: string
}

export type PageFolder = {
  slug: string
  title: string
  description: string
  pages: Page[]
}

export interface Step {
  title: string
  content: string
}

export type ServicePreview = {
  id: string
  title: string
  text: string
  path: string
}

export type AscensionLog = {
  id: string
  title: string
  description: ReactNode
}
