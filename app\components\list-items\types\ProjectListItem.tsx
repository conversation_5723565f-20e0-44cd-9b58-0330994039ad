import { Project } from '@/app/types/types'
import ListItemThumbnail from '@/app/components/list-items/ListItemThumbnail'
import ListItemDescription from '@/app/components/list-items/ListItemDescription'
import ListItemHeading from '@/app/components/list-items/ListItemHeading'
import { memo } from 'react'

type Props = Pick<Project, 'title' | 'description' | 'thumbnail'>

const ProjectListItem = ({ title, thumbnail, description }: Props) => (
  <>
    <ListItemHeading>{title}</ListItemHeading>
    <ListItemThumbnail thumbnail={thumbnail} title={title} />
    <ListItemDescription className="my-0">
      <p>{description}</p>
    </ListItemDescription>
  </>
)

export default memo(ProjectListItem)
