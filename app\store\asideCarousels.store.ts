import { create } from 'zustand'
import { CarouselApi } from '@/components/ui/carousel'
import { AutoplayType } from 'embla-carousel-autoplay'
import { useRandomCarouselScrollManager } from '@/app/hooks/useRandomCarouselScrollManager'
import { Feedback, PageFolder, Project } from '@/app/types/types'

const defaultCarouselState = <T>(): CarouselState<T> => ({
  carouselData: [] as T[],
  plugins: [] as AutoplayType[],
  setApi: () => {},
  stopAutoplay: () => {},
  startAutoplay: () => {},
  activeCarouselIndex: 0,
})

type CarouselState<T> = {
  carouselData: T[]
  plugins: AutoplayType[]
  setApi: (api: CarouselApi | undefined) => void
  stopAutoplay: () => void
  startAutoplay: () => void
  activeCarouselIndex: number
}

type CarouselStates = {
  serviceCarousel: CarouselState<PageFolder>
  feedbackCarousel: CarouselState<Feedback>
  projectCarousel: CarouselState<Project>
}

type SetCarouselStatesPayload = ReturnType<typeof useRandomCarouselScrollManager> & {
  services: PageFolder[]
  clientFeedback: Feedback[]
  projects: Project[]
}

type AsideCarouselStore = {
  carouselStates: CarouselStates
  setCarouselStates: (payload: SetCarouselStatesPayload) => void
  resetCarouselStates: () => void
}

export const useAsideCarouselStore = create<AsideCarouselStore>(set => ({
  carouselStates: {
    serviceCarousel: defaultCarouselState<PageFolder>(),
    feedbackCarousel: defaultCarouselState<Feedback>(),
    projectCarousel: defaultCarouselState<Project>(),
  },
  setCarouselStates: ({
    services,
    clientFeedback,
    projects,
    plugins,
    setApi,
    stopAutoplay,
    startAutoplay,
    activeCarouselIndexes,
  }) => {
    set({
      carouselStates: {
        serviceCarousel: {
          carouselData: services,
          plugins: [plugins[0]],
          setApi: setApi(0),
          stopAutoplay,
          startAutoplay,
          activeCarouselIndex: activeCarouselIndexes[0],
        },
        feedbackCarousel: {
          carouselData: clientFeedback,
          plugins: [plugins[1]],
          setApi: setApi(1),
          stopAutoplay,
          startAutoplay,
          activeCarouselIndex: activeCarouselIndexes[1],
        },
        projectCarousel: {
          carouselData: projects,
          plugins: [plugins[2]],
          setApi: setApi(2),
          stopAutoplay,
          startAutoplay,
          activeCarouselIndex: activeCarouselIndexes[2],
        },
      },
    })
  },
  resetCarouselStates: () => {
    set(state => {
      // Stop any running carousels before resetting
      try {
        if (typeof state.carouselStates.serviceCarousel.stopAutoplay === 'function') {
          state.carouselStates.serviceCarousel.stopAutoplay()
        }
        if (typeof state.carouselStates.feedbackCarousel.stopAutoplay === 'function') {
          state.carouselStates.feedbackCarousel.stopAutoplay()
        }
        if (typeof state.carouselStates.projectCarousel.stopAutoplay === 'function') {
          state.carouselStates.projectCarousel.stopAutoplay()
        }
      } catch {
        // Silently handle any errors during cleanup
      }

      return {
        carouselStates: {
          serviceCarousel: defaultCarouselState<PageFolder>(),
          feedbackCarousel: defaultCarouselState<Feedback>(),
          projectCarousel: defaultCarouselState<Project>(),
        },
      }
    })
  },
}))
