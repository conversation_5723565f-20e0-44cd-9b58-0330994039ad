import { create } from 'zustand'
import { CarouselApi } from '@/components/ui/carousel'
import { AutoplayType } from 'embla-carousel-autoplay'
import { useRandomCarouselScrollManager } from '@/app/hooks/useRandomCarouselScrollManager'
import { Feedback, PageFolder, Project } from '@/app/types/types'

const defaultCarouselState = <T>(): CarouselState<T> => ({
  carouselData: [] as T[],
  plugins: [] as AutoplayType[],
  setApi: () => {},
  stopAutoplay: () => {},
  startAutoplay: () => {},
  activeCarouselIndex: 0,
})

type CarouselState<T> = {
  carouselData: T[]
  plugins: AutoplayType[]
  setApi: (api: CarouselApi | undefined) => void
  stopAutoplay: () => void
  startAutoplay: () => void
  activeCarouselIndex: number
}

type CarouselStates = {
  serviceCarousel: CarouselState<PageFolder>
  feedbackCarousel: CarouselState<Feedback>
  projectCarousel: CarouselState<Project>
}

type SetCarouselStatesPayload = ReturnType<typeof useRandomCarouselScrollManager> & {
  services: PageFolder[]
  clientFeedback: Feedback[]
  projects: Project[]
}

type AsideCarouselStore = {
  carouselStates: CarouselStates
  setCarouselStates: (payload: SetCarouselStatesPayload) => void
  resetCarouselStates: () => void
}

export const useAsideCarouselStore = create<AsideCarouselStore>(set => ({
  carouselStates: {
    serviceCarousel: defaultCarouselState<PageFolder>(),
    feedbackCarousel: defaultCarouselState<Feedback>(),
    projectCarousel: defaultCarouselState<Project>(),
  },
  setCarouselStates: ({
    services,
    clientFeedback,
    projects,
    plugins,
    setApi,
    stopAutoplay,
    startAutoplay,
    activeCarouselIndexes,
  }) => {
    set({
      carouselStates: {
        serviceCarousel: {
          carouselData: services,
          plugins: [plugins[0]],
          setApi: setApi(0),
          stopAutoplay,
          startAutoplay,
          activeCarouselIndex: activeCarouselIndexes[0],
        },
        feedbackCarousel: {
          carouselData: clientFeedback,
          plugins: [plugins[1]],
          setApi: setApi(1),
          stopAutoplay,
          startAutoplay,
          activeCarouselIndex: activeCarouselIndexes[1],
        },
        projectCarousel: {
          carouselData: projects,
          plugins: [plugins[2]],
          setApi: setApi(2),
          stopAutoplay,
          startAutoplay,
          activeCarouselIndex: activeCarouselIndexes[2],
        },
      },
    })
  },
  resetCarouselStates: () => {
    set(state => {
      // Stop any running carousels before resetting
      try {
        state.carouselStates.serviceCarousel.stopAutoplay?.()
        state.carouselStates.feedbackCarousel.stopAutoplay?.()
        state.carouselStates.projectCarousel.stopAutoplay?.()
      } catch (error) {
        console.warn('Error stopping carousels during reset:', error)
      }

      return {
        carouselStates: {
          serviceCarousel: defaultCarouselState<PageFolder>(),
          feedbackCarousel: defaultCarouselState<Feedback>(),
          projectCarousel: defaultCarouselState<Project>(),
        },
      }
    })
  },
}))
