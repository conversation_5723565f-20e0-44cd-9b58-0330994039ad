import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses'
import { NextResponse } from 'next/server'
import * as z from 'zod'

const sesClient = new SESClient({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
})

type SendEmailOptions = {
  subject: string
  body: string
}

export async function sendEmail({ subject, body }: SendEmailOptions) {
  const params = {
    Source: process.env.EMAIL_FROM!,
    Destination: {
      ToAddresses: [process.env.EMAIL_TO!],
    },
    Message: {
      Subject: {
        Data: subject,
      },
      Body: {
        Text: { Data: body },
      },
    },
  }

  const command = new SendEmailCommand(params)
  const response = await sesClient.send(command)

  return response
}

type EmailConfigProps<T extends z.ZodObject> = {
  subject: string
  formType: string
  formatBody: (data: z.infer<T>) => string
}

type ProcessEmailRequestProps<T extends z.ZodObject> = {
  req: Request
  schema: T
  emailConfig: EmailConfigProps<T>
}

export async function processEmailRequest<T extends z.ZodObject>({
  req,
  schema,
  emailConfig,
}: ProcessEmailRequestProps<T>) {
  try {
    const data = await req.json()
    const result: z.ZodSafeParseResult<z.infer<T>> = schema.safeParse(data)

    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid form data', details: result.error.issues },
        { status: 400 }
      )
    }

    const messageBody = emailConfig.formatBody(result.data)

    const response = await sendEmail({
      subject: emailConfig.subject,
      body: messageBody,
    })

    return NextResponse.json({
      message: `${emailConfig.formType} successfully processed`,
      messageId: response.MessageId,
    })
  } catch (error) {
    return NextResponse.json(
      {
        error: `Failed to process ${emailConfig.formType.toLowerCase()}`,
        details: (error as Error).message,
      },
      { status: 500 }
    )
  }
}
