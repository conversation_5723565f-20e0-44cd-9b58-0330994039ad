import { FormDescription, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { FieldError, FieldValues, Path, UseFormRegister } from 'react-hook-form'

type Props<TFieldValues extends FieldValues> = {
  register: UseFormRegister<TFieldValues>
  name: Path<TFieldValues>
  label?: string
  description?: string
  placeholder?: string
  error?: FieldError
}

const InputField = <TFieldValues extends FieldValues>({
  register,
  name,
  label,
  description,
  placeholder,
  error,
}: Props<TFieldValues>) => {
  return (
    <FormItem>
      {label && <FormLabel htmlFor={name}>{label}</FormLabel>}
      <Input {...register(name)} placeholder={placeholder} />
      {description && <FormDescription>{description}</FormDescription>}
      <FormMessage>{error?.message}</FormMessage>
    </FormItem>
  )
}

export default InputField
