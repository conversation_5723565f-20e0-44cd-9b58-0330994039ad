import { HTMLAttributes } from 'react'
import { cn } from '@/lib/utils'
import CardOverlayLabel from '@/app/components/CardOverlayLabel'

export type Props = {
  cardOverlayLabel: string
  link?: string
  className?: string
  closeCondition?: boolean
} & HTMLAttributes<HTMLDivElement>

const OverlayLayout = ({ cardOverlayLabel, link, closeCondition, className, ...props }: Props) => {
  return (
    <div
      className={cn(
        '!absolute bottom-0 inset-x-0 z-20 min-h-20',
        '!bg-black/75 backdrop-blur-sm sm:rounded',
        'transition-all duration-500 ease-in-out',
        'text-white text-left *:!font-semibold',
        closeCondition && '!translate-y-full'
      )}
      {...props}
    >
      <CardOverlayLabel cardOverlayLabel={cardOverlayLabel} link={link} />
      <div className={cn('transition-all duration-500 ease-in-out', className)}>
        {props.children}
      </div>
    </div>
  )
}

export default OverlayLayout
