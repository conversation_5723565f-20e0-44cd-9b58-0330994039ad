import Link from 'next/link'
import <PERSON>ItemWrapper from '@/app/components/list-items/ListItemWrapper'
import { ReactNode } from 'react'

type Props = {
  children: ReactNode
  path?: string
}

const ListItem = ({ children, path }: Props) => {
  const ItemContent = <ListItemWrapper>{children}</ListItemWrapper>

  if (path) {
    return (
      <Link href={path} className="h-full inline-block">
        {ItemContent}
      </Link>
    )
  }

  return <>{ItemContent}</>
}

export default ListItem
