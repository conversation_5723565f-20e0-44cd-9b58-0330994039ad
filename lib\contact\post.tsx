import { ChallengeFormType } from '@/app/components/forms/ChallengeForm/ChallengeSchema'
import { CooperationFormType } from '@/app/components/forms/CooperationForm/CooperationSchema'
import { EmailSubscriptionFormType } from '@/app/components/forms/EmailSubscriptionForm/EmailSubscriptionSchema'

type JSONValue = string | number | boolean | null | JSONValue[] | { [key: string]: JSONValue }

type PostBody = Record<string, JSONValue>

export const post = async <T extends PostBody>(url: string, values: T) => {
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(values),
  })

  if (!response.ok) {
    throw new Error('Failed to submit form')
  }
}

export const postEmailSubscription = (values: EmailSubscriptionFormType) =>
  post('/api/emailSubscription', values)

export const postCooperation = (values: CooperationFormType) => post('/api/cooperation', values)

export const postChallenge = (values: ChallengeFormType) => post('/api/challenge', values)
