import { CarouselApi } from '@/components/ui/carousel'
import Autoplay from 'embla-carousel-autoplay'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'

export const useRandomCarouselScrollManager = (
  dataArrays: unknown[][],
  autoSlideDelay: number = 500
) => {
  const apiRefs = useRef<(CarouselApi | undefined)[]>([])
  const [apiInitialized, setApiInitialized] = useState(false)
  const pluginsRef = useRef<ReturnType<typeof Autoplay>[]>([])
  const [activeCarouselIndexes, setActiveCarouselIndexes] = useState<number[]>([])
  const activeCarousel = useRef(0)
  const firstPlay = useRef(false)
  const initialized = useRef(false)

  if (!initialized.current) {
    pluginsRef.current = dataArrays.map(() =>
      Autoplay({ delay: autoSlideDelay, stopOnInteraction: true, playOnInit: false })
    )
    setActiveCarouselIndexes(dataArrays.map(() => 0))
    initialized.current = true
  }

  const setApi = useCallback(
    (index: number) => (api: CarouselApi | undefined) => {
      apiRefs.current[index] = api
      setApiInitialized(true)
    },
    []
  )

  const validDataArrays = useMemo(() => {
    const validDataArrays = dataArrays
      .map((data, index) => ({ valid: data && data.length > 1, index }))
      .filter(item => item.valid)
      .map(item => item.index)
    return validDataArrays
  }, [dataArrays])

  const stopAllCarousels = useCallback(() => {
    validDataArrays.forEach(index => {
      const plugin = pluginsRef.current[index]
      if (plugin && typeof plugin.stop === 'function') {
        try {
          plugin.stop()
        } catch (error) {
          console.warn('Failed to stop carousel:', error)
        }
      }
    })
  }, [validDataArrays])

  const playCarousel = useCallback((nextCarouselIndex: number) => {
    const plugin = pluginsRef.current[nextCarouselIndex]
    if (plugin && typeof plugin.play === 'function') {
      try {
        plugin.play()
      } catch (error) {
        console.warn('Failed to play carousel:', error)
      }
    }
  }, [])

  const selectRandomCarousel = useCallback(() => {
    let availableCarousels = [...validDataArrays]
    if (activeCarousel.current !== null && availableCarousels.length > 1) {
      availableCarousels = availableCarousels.filter(n => n !== activeCarousel.current)
    }
    activeCarousel.current =
      availableCarousels[Math.floor(Math.random() * availableCarousels.length)]
  }, [activeCarousel, validDataArrays])

  const stopAutoplay = useCallback(() => {
    stopAllCarousels()
  }, [stopAllCarousels])

  const startAutoplay = useCallback(() => {
    playCarousel(activeCarousel.current)
  }, [playCarousel])

  const handleSelect = useCallback(() => {
    stopAllCarousels()
    selectRandomCarousel()
    playCarousel(activeCarousel.current)
  }, [playCarousel, selectRandomCarousel, stopAllCarousels])

  useEffect(() => {
    if (!apiInitialized) return

    const unbinds = apiRefs.current.map((api, index) => {
      if (!api) return () => {}
      api.on('select', () => {
        setActiveCarouselIndexes(prev => {
          prev[index] = api.selectedScrollSnap()
          return [...prev]
        })
      })
      return () => api.off('select', () => {})
    })

    return () => {
      unbinds.forEach(unbind => unbind())
    }
  }, [apiInitialized])

  useEffect(() => {
    if (!apiInitialized) return

    const unbinds = apiRefs.current.map(api => {
      if (!api) return () => {}
      api.on('autoplay:select', handleSelect)
      api.on('reInit', handleSelect)
      return () => {
        api.off('autoplay:select', handleSelect)
        api.off('reInit', handleSelect)
      }
    })

    return () => {
      unbinds.forEach(unbind => unbind())
    }
  }, [apiInitialized, handleSelect])

  useEffect(() => {
    if (!apiInitialized) return
    if (firstPlay.current) return
    firstPlay.current = true

    selectRandomCarousel()
    playCarousel(activeCarousel.current)
  }, [apiInitialized, playCarousel, selectRandomCarousel])

  // Cleanup effect to stop all carousels when component unmounts
  useEffect(() => {
    return () => {
      stopAllCarousels()
    }
  }, [stopAllCarousels])

  return {
    plugins: pluginsRef.current,
    setApi,
    stopAutoplay,
    startAutoplay,
    activeCarouselIndexes,
  }
}
