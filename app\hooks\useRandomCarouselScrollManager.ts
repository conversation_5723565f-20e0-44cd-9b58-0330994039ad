import { CarouselApi } from '@/components/ui/carousel'
import Autoplay from 'embla-carousel-autoplay'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'

export const useRandomCarouselScrollManager = (
  dataArrays: unknown[][],
  autoSlideDelay: number = 2000
) => {
  const apiRefs = useRef<(CarouselApi | undefined)[]>([])
  const [apiInitialized, setApiInitialized] = useState(false)
  const pluginsRef = useRef<ReturnType<typeof Autoplay>[]>([])
  const [activeCarouselIndexes, setActiveCarouselIndexes] = useState<number[]>([])
  const activeCarousel = useRef(0)
  const firstPlay = useRef(false)
  const initialized = useRef(false)
  const initializationCount = useRef(0)

  // Initialize plugins when dataArrays change
  if (!initialized.current || initializationCount.current !== dataArrays.length) {
    pluginsRef.current = dataArrays.map(() =>
      Autoplay({ delay: autoSlideDelay, stopOnInteraction: true, playOnInit: false })
    )
    setActiveCarouselIndexes(dataArrays.map(() => 0))
    initialized.current = true
    initializationCount.current = dataArrays.length
    setApiInitialized(false) // Reset API initialization when plugins change
    firstPlay.current = false // Reset first play flag
  }

  const setApi = useCallback(
    (index: number) => (api: CarouselApi | undefined) => {
      apiRefs.current[index] = api

      // Check if all expected APIs are now set
      const expectedApiCount = dataArrays.length
      const setApiCount = apiRefs.current.filter(api => api !== undefined).length

      console.log('API set:', { index, expectedApiCount, setApiCount, apiInitialized })

      if (setApiCount >= expectedApiCount && !apiInitialized) {
        console.log('All APIs initialized, setting apiInitialized to true')
        setApiInitialized(true)
      }
    },
    [apiInitialized, dataArrays.length]
  )

  const validDataArrays = useMemo(() => {
    const validDataArrays = dataArrays
      .map((data, index) => ({ valid: data && data.length > 1, index }))
      .filter(item => item.valid)
      .map(item => item.index)
    return validDataArrays
  }, [dataArrays])

  const stopAllCarousels = useCallback(() => {
    validDataArrays.forEach(index => {
      const plugin = pluginsRef.current[index]
      if (plugin) {
        try {
          plugin.stop()
        } catch {
          // Silently handle the error - plugin might not be initialized yet
        }
      }
    })
  }, [validDataArrays])

  const playCarousel = useCallback((nextCarouselIndex: number) => {
    const plugin = pluginsRef.current[nextCarouselIndex]
    const api = apiRefs.current[nextCarouselIndex]

    console.log('playCarousel called:', { nextCarouselIndex, hasPlugin: !!plugin, hasApi: !!api })

    if (plugin && api) {
      try {
        plugin.play()
        console.log('Autoplay started for carousel:', nextCarouselIndex)
      } catch (error) {
        console.warn('Failed to start autoplay:', error)
      }
    }
  }, [])

  const selectRandomCarousel = useCallback(() => {
    let availableCarousels = [...validDataArrays]
    if (activeCarousel.current !== null && availableCarousels.length > 1) {
      availableCarousels = availableCarousels.filter(n => n !== activeCarousel.current)
    }
    activeCarousel.current =
      availableCarousels[Math.floor(Math.random() * availableCarousels.length)]
  }, [activeCarousel, validDataArrays])

  const stopAutoplay = useCallback(() => {
    stopAllCarousels()
  }, [stopAllCarousels])

  const startAutoplay = useCallback(() => {
    playCarousel(activeCarousel.current)
  }, [playCarousel])

  const handleSelect = useCallback(() => {
    stopAllCarousels()
    selectRandomCarousel()
    playCarousel(activeCarousel.current)
  }, [playCarousel, selectRandomCarousel, stopAllCarousels])

  useEffect(() => {
    if (!apiInitialized) return

    const unbinds = apiRefs.current.map((api, index) => {
      if (!api) return () => {}
      api.on('select', () => {
        setActiveCarouselIndexes(prev => {
          prev[index] = api.selectedScrollSnap()
          return [...prev]
        })
      })
      return () => api.off('select', () => {})
    })

    return () => {
      unbinds.forEach(unbind => unbind())
    }
  }, [apiInitialized])

  useEffect(() => {
    if (!apiInitialized) return

    const unbinds = apiRefs.current.map(api => {
      if (!api) return () => {}
      api.on('autoplay:select', handleSelect)
      api.on('reInit', handleSelect)
      return () => {
        api.off('autoplay:select', handleSelect)
        api.off('reInit', handleSelect)
      }
    })

    return () => {
      unbinds.forEach(unbind => unbind())
    }
  }, [apiInitialized, handleSelect])

  useEffect(() => {
    console.log('Autoplay start effect:', { apiInitialized, firstPlay: firstPlay.current, validDataArraysLength: validDataArrays.length })

    if (!apiInitialized) return
    if (firstPlay.current) return
    if (validDataArrays.length === 0) return

    firstPlay.current = true
    console.log('Starting autoplay initialization...')

    // Add a delay to ensure the carousel API is fully initialized
    const timer = setTimeout(() => {
      selectRandomCarousel()
      console.log('Selected carousel:', activeCarousel.current)
      if (validDataArrays.length > 0) {
        playCarousel(activeCarousel.current)
      }
    }, 500) // Increased delay for better reliability

    return () => clearTimeout(timer)
  }, [apiInitialized, playCarousel, selectRandomCarousel, validDataArrays.length])

  // Cleanup effect to stop all carousels when component unmounts
  useEffect(() => {
    return () => {
      stopAllCarousels()
      // Reset the refs to prevent stale references
      apiRefs.current = []
      firstPlay.current = false
      initialized.current = false
    }
  }, [stopAllCarousels])

  return {
    plugins: pluginsRef.current,
    setApi,
    stopAutoplay,
    startAutoplay,
    activeCarouselIndexes,
  }
}
