import { BaseSchema } from '@/app/components/forms/BaseSchema'
import { z } from 'zod'

export const challengeOptions = [
  {
    value: 'misfit-solutions',
    label: "Misfit Solutions: When ready-made options just don't capture your vision.",
  },
  {
    value: 'code-mess',
    label: 'The Code Mess: When custom solutions become a jumbled mess of code.',
  },
  {
    value: 'resource-drain',
    label: 'Resource Drain: When delays threaten both your time and budget.',
  },
  { value: 'momentum-loss', label: 'Momentum Loss: When the initial spark starts to wane.' },
  { value: 'own-challenge', label: 'Your very own challenge:' },
] as const

export type ChallengeValue = (typeof challengeOptions)[number]['value']
export const challengeValues = challengeOptions.map(item => item.value) as [
  ChallengeValue,
  ...ChallengeValue[],
]

export const ChallengeSchema = BaseSchema.extend({
  challenge: z.enum(challengeValues),
  yourChallenge: z.string().optional(),
}).check(({ value: { challenge, yourChallenge }, issues }) => {
  if (challenge === 'own-challenge' && (!yourChallenge || yourChallenge.trim().length < 10)) {
    issues.push({
      code: 'custom',
      message:
        'Please describe your challenge when selecting your own challenge at least in a couple of sentences.',
      input: 'yourChallenge',
      path: ['yourChallenge'],
    })
  }
})

export type ChallengeFormType = z.infer<typeof ChallengeSchema>
