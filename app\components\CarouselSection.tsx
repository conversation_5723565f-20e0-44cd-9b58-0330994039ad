import CarouselColumn from '@/app/components/CarouselColumn'
import ProjectCarouselCard from '@/app/components/carousel-cards/ProjectCarouselCard'
import ServiceCarouselCard from '@/app/components/carousel-cards/ServiceCarouselCard'
import ClientFeedbackCarouselCard from '@/app/components/carousel-cards/ClientFeedbackCarouselCard'
import { cn } from '@/lib/utils'
import PageSection from '@/app/components/ui/PageSection'

const CarouselSection = () => (
  <PageSection
    as="aside"
    className={cn(
      'grid-in-carousel',
      'max-xl:contents',
      'xl:flex tall:flex-col',
      'xl:sticky top-0',
      'max-w-[50vw] w-fit h-screen gap-x-10 pb-6'
    )}
  >
    <CarouselColumn
      className={cn(
        'max-xl:contents',
        '*:max-w-2xl *:xl:max-w-md tall:h-3/5 mx-auto',
        '*:first:grid-in-service',
        '*:last:grid-in-feedback'
      )}
    >
      <ServiceCarouselCard />
      <ClientFeedbackCarouselCard />
    </CarouselColumn>
    <CarouselColumn
      className={cn(
        'max-xl:contents',
        '*:max-w-2xl *:xl:max-w-md tall:h-2/5 mx-auto',
        '*:first:grid-in-project'
      )}
    >
      <ProjectCarouselCard />
    </CarouselColumn>
  </PageSection>
)

export default CarouselSection
