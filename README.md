This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/basic-features/font-optimization) to automatically optimize and load Inter, a custom Google Font.

## Testing

This project uses both Jest for unit testing and Cypress for end-to-end testing to ensure code quality and functionality.

### Jest Testing

Jest is configured for unit and component testing with the following setup:
- **Test Environment**: jsdom for DOM testing
- **Setup**: Custom Jest configuration with Next.js integration
- **Coverage**: Comprehensive coverage reporting for app, components, and lib directories
- **Mocking**: Pre-configured mocks for browser APIs and UI components

#### Running Jest Tests

**Prerequisites**
```bash
yarn install
```

**Run All Tests**
```bash
yarn test
```

**Run Tests in Watch Mode**
```bash
yarn test:watch
```

**Run Tests with Coverage**
```bash
yarn test:coverage
```

**Update Snapshots**
```bash
yarn test -u
```

### Cypress Testing

Cypress is configured for end-to-end testing with the following setup:
- **Viewport**: 1600x1080
- **Timeouts**: 30-second timeouts for commands, page loads, and requests
- **Test Files**: Located in `cypress/e2e/` directory

#### Running Cypress Tests

**Prerequisites**
```bash
yarn install
yarn dev
```

**Open Cypress Test Runner (Interactive Mode)**
```bash
npx cypress open
```

**Run All Tests (Terminal Mode)**
```bash
npx cypress run
```

**Run Specific Test File**
```bash
npx cypress run --spec "cypress/e2e/all-links.cy.ts" --headless
```

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.
