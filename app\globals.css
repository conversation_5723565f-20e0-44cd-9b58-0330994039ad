@import 'tailwindcss';
@import 'tw-animate-css';
@plugin '@tailwindcss/typography';

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(0, 0%, 3.9%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(0, 0%, 3.9%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(0, 0%, 3.9%);
  --primary: hsl(0, 0%, 9%);
  --primary-foreground: hsl(0, 0%, 98%);
  --secondary: hsl(0, 0%, 96.1%);
  --secondary-foreground: hsl(0, 0%, 9%);
  --muted: hsl(0, 0%, 96.1%);
  --muted-foreground: hsl(0, 0%, 45.1%);
  --accent: hsl(0, 0%, 96.1%);
  --accent-foreground: hsl(0, 0%, 9%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --border: hsl(0, 0%, 89.8%);
  --input: hsl(0, 0%, 89.8%);
  --ring: hsl(0, 0%, 3.9%);
  --chart-1: hsl(12, 76%, 61%);
  --chart-2: hsl(173, 58%, 39%);
  --chart-3: hsl(197, 37%, 24%);
  --chart-4: hsl(43, 74%, 66%);
  --chart-5: hsl(27, 87%, 67%);
  --color-1: hsl(0, 100%, 63%);
  --color-2: hsl(270, 100%, 63%);
  --color-3: hsl(210, 100%, 63%);
  --color-4: hsl(195, 100%, 63%);
  --color-5: hsl(90, 100%, 63%);
}

.dark {
  --background: hsl(0, 0%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --card: hsl(0, 0%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --popover: hsl(0, 0%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --primary: hsl(0, 0%, 98%);
  --primary-foreground: hsl(0, 0%, 9%);
  --secondary: hsl(0, 0%, 14.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --muted: hsl(0, 0%, 14.9%);
  --muted-foreground: hsl(0, 0%, 63.9%);
  --accent: hsl(0, 0%, 14.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --border: hsl(0, 0%, 14.9%);
  --input: hsl(0, 0%, 14.9%);
  --ring: hsl(0, 0%, 83.1%);
  --chart-1: hsl(220, 70%, 50%);
  --chart-2: hsl(160, 60%, 45%);
  --chart-3: hsl(30, 80%, 55%);
  --chart-4: hsl(280, 65%, 60%);
  --chart-5: hsl(340, 75%, 55%);
  --color-1: hsl(0, 100%, 63%);
  --color-2: hsl(270, 100%, 63%);
  --color-3: hsl(210, 100%, 63%);
  --color-4: hsl(195, 100%, 63%);
  --color-5: hsl(90, 100%, 63%);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-color-1: var(--color-1);
  --color-color-2: var(--color-2);
  --color-color-3: var(--color-3);
  --color-color-4: var(--color-4);
  --color-color-5: var(--color-5);

  --animate-shimmer: shimmer var(--speed) ease-in-out infinite alternate;
  --animate-spin-around: spin-aroundvar calc(var(--speed) * 2) infinite linear;
  --animate-rainbow: rainbow var(--speed, 2s) infinite linear;
  --animate-accordion-down: accordion-down 0.2s ease-in-out;
  --animate-accordion-up: accordion-up 0.2s ease-in-out;
  --animate-accordion-right: accordion-right 0.2s ease-in-out;
}

@custom-variant dark (&:where(.dark, .dark *));
@custom-variant tall (@media (height >= 1440px));

@layer base {
  * {
    border-color: var(--border);
  }
  body {
    background-color: var(--background);
    color: var(--color-gray-800);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }
  button,
  [type="button"],
  [role="button"],
  a {
    cursor: pointer;
  }
}

@theme {
  @keyframes shimmer {
    to {
      transform: translate(calc(100cqw - 100%), 0);
    }
  }
  @keyframes spin-around {
    0%,
    100% {
      transform: translateZ(0) rotate(0);
    }
    15%,
    35% {
      transform: translateZ(0) rotate(90deg);
    }
    65%,
    85% {
      transform: translateZ(0) rotate(270deg);
    }
  }
  @keyframes rainbow {
    0%,
    100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }
  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
  @keyframes accordion-right {
    from {
      width: calc(var(--radix-accordion-content-width) * 2);
    }
    to {
      width: 0;
    }
  }
}

@utility grid-areas-layout-home-tall {
  grid-template-areas:
    'hero service'
    'hero feedback'
    'hero project'
    'roadblocks .'
    'ascension-logs .'
    'application-development-intro .'
    'cards-plan .'
    'service-accordion .'
}
@utility grid-areas-layout-home-desktop {
  grid-template-areas:
    'hero feedback project' 
    'hero service project' 
    'roadblocks . .' 
    'ascension-logs . .' 
    'application-development-intro . .' 
    'cards-plan . .' 
    'service-accordion . .'
}
@utility grid-areas-layout-home-mobile {
  grid-template-areas:
    'hero'
    'project' 
    'roadblocks'
    'feedback'
    'ascension-logs'
    'application-development-intro'
    'cards-plan'
    'service-accordion'
    'service'
}
@utility grid-areas-layout-mobile {
  grid-template-areas: 'content' 'service' 'feedback' 'project';
}
@utility grid-areas-layout {
  grid-template-areas: 'content carousel';
}
@utility grid-cols-layout-mobile { grid-template-columns: minmax(0, 1fr); }
@utility grid-cols-layout { grid-template-columns: 1fr auto; }
@utility grid-in-content { grid-area: content; }
@utility grid-in-carousel { grid-area: carousel; }
@utility grid-in-hero { grid-area: hero; }
@utility grid-in-project { grid-area: project; }
@utility grid-in-roadblocks { grid-area: roadblocks; }
@utility grid-in-ascension-logs { grid-area: ascension-logs; }
@utility grid-in-application-development-intro { grid-area: application-development-intro; }
@utility grid-in-cards-plan { grid-area: cards-plan; }
@utility grid-in-service-accordion { grid-area: service-accordion; }
@utility grid-in-feedback { grid-area: feedback; }
@utility grid-in-service { grid-area: service; }
