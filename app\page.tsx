import { Metadata } from 'next'
import Roadblocks from '@/app/components/Roadblocks'
import AscensionLogs from '@/app/components/AscensionLogs'
import ApplicationDevelopmentIntro from '@/app/components/ApplicationDevelopmentIntro'
import PlanSection from '@/app/components/PlanSection'
import ServicesSection from '@/app/components/ServicesSection'
import Hero from '@/app/components/Hero'
import PageSection from '@/app/components/ui/PageSection'
import ProjectCarouselCard from '@/app/components/carousel-cards/ProjectCarouselCard'
import ClientFeedbackCarouselCard from '@/app/components/carousel-cards/ClientFeedbackCarouselCard'
import ServiceCarouselCard from '@/app/components/carousel-cards/ServiceCarouselCard'
import AsideWrapper from '@/app/components/AsideWrapper'

export const metadata: Metadata = {
  title: 'manystack - Building your dream app for web and mobile',
  description:
    'From sketch to launch, your dream app deserves a team that gets it. Whether web or mobile, Manystack builds every layer with care—ready to ascend your concept into the cloud and into the hands of real users.',
  openGraph: {
    description: 'Your dream app, built to ascend—web, mobile, and beyond.',
  },
}

const Home = () => (
  <>
    <PageSection className="grid-in-hero">
      <Hero />
    </PageSection>
    <AsideWrapper className="grid-in-project xl:pb-6 xl:h-screen xl:tall:h-[40vh]">
      <ProjectCarouselCard />
    </AsideWrapper>
    <PageSection className="grid-in-roadblocks">
      <Roadblocks />
    </PageSection>
    <AsideWrapper className="grid-in-service xl:pb-6 xl:tall:pb-0 xl:h-[50vh] xl:tall:h-[30vh]">
      <ServiceCarouselCard />
    </AsideWrapper>
    {/* <PageSection className="grid-in-ascension-logs">
      <AscensionLogs />
    </PageSection> */}
    <PageSection className="grid-in-application-development-intro">
      <ApplicationDevelopmentIntro />
    </PageSection>
    {/* <PageSection className="grid-in-cards-plan">
      <PlanSection />
    </PageSection> */}
    <PageSection className="grid-in-service-accordion xl:border-b-0">
      <ServicesSection />
    </PageSection>
    <AsideWrapper className="grid-in-feedback xl:pb-0 xl:h-[50vh] xl:tall:h-[30vh]">
      <ClientFeedbackCarouselCard />
    </AsideWrapper>
  </>
)

export default Home
