'use client'

import { HTMLAttributes, ReactNode, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import isHomepage from '@/lib/isHomepage'
import CarouselSection from '@/app/components/CarouselSection'
import { cn } from '@/lib/utils'
import Breadcrumb from '@/app/components/Breadcrumb'
import { Feedback, PageFolder, Project } from '@/app/types/types'
import { useAsideCarouselStore } from '@/app/store/asideCarousels.store'
import { useShallow } from 'zustand/react/shallow'
import { useRandomCarouselScrollManager } from '@/app/hooks/useRandomCarouselScrollManager'

type Props = {
  children: ReactNode
  services: PageFolder[]
  clientFeedback: Feedback[]
  projects: Project[]
}

const GridLayout = ({ children, services, clientFeedback, projects }: Props) => {
  const pathname = usePathname()
  const isHome = isHomepage(pathname)

  return (
    <div
      className={cn(
        !isHome &&
          'grid-areas-layout-mobile xl:grid-areas-layout grid-cols-layout-mobile xl:grid-cols-layout gap-x-10',
        'grid flex-1',
        'max-xl:mx-auto pb-10'
      )}
    >
      {isHome ? (
        <HomeGridLayout services={services} clientFeedback={clientFeedback} projects={projects}>
          {children}
        </HomeGridLayout>
      ) : (
        <DefaultGridLayout services={services} clientFeedback={clientFeedback} projects={projects}>
          {children}
        </DefaultGridLayout>
      )}
    </div>
  )
}

export default GridLayout

type GridProps = {
  children: ReactNode
  services: PageFolder[]
  clientFeedback: Feedback[]
  projects: Project[]
}

const HomeGridLayout = ({ children, services, clientFeedback, projects }: GridProps) => {
  console.log('Home')
  const carouselManager = useRandomCarouselScrollManager([services, clientFeedback, projects])
  const { setCarouselStates, resetCarouselStates } = useAsideCarouselStore(
    useShallow(state => ({
      setCarouselStates: state.setCarouselStates,
      resetCarouselStates: state.resetCarouselStates,
    }))
  )

  useEffect(() => {
    setCarouselStates({
      ...carouselManager,
      services,
      clientFeedback,
      projects,
    })

    return resetCarouselStates
  }, [services, clientFeedback, projects, setCarouselStates, carouselManager, resetCarouselStates])

  return (
    <GridContentWrapper
      className={cn(
        'grid gap-x-10',
        'grid-areas-layout-home-mobile xl:grid-areas-layout-home-desktop xl:tall:grid-areas-layout-home-tall',
        'grid-cols-layout-mobile xl:grid-cols-layout'
      )}
    >
      {children}
    </GridContentWrapper>
  )
}

const DefaultGridLayout = ({ children, services, clientFeedback, projects }: GridProps) => {
  console.log('Default')
  const carouselManager = useRandomCarouselScrollManager([services, clientFeedback, projects])
  const { setCarouselStates, resetCarouselStates } = useAsideCarouselStore(
    useShallow(state => ({
      setCarouselStates: state.setCarouselStates,
      resetCarouselStates: state.resetCarouselStates,
    }))
  )

  useEffect(() => {
    setCarouselStates({
      ...carouselManager,
      services,
      clientFeedback,
      projects,
    })

    return resetCarouselStates
  }, [services, clientFeedback, projects, setCarouselStates, carouselManager, resetCarouselStates])

  return (
    <>
      <GridContentWrapper
        className={cn(
          'flex flex-col items-center',
          'grid-in-content',
          '*:max-xl:last:!mb-10 *:max-xl:last:!border-b-2'
        )}
      >
        <Breadcrumb pages={services} />
        {children}
      </GridContentWrapper>
      <CarouselSection />
    </>
  )
}

const GridContentWrapper = ({ className, ...props }: HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      '*:max-w-2xl *:xl:max-w-4xl *:w-full *:mx-auto *:py-10 w-full',
      'divide-y-2 divide-bg-border',
      className
    )}
    {...props}
  />
)

export { GridContentWrapper }
