'use client'

import React, { <PERSON>actN<PERSON>, useState, createContext, useCallback, useContext } from 'react'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import confetti from 'canvas-confetti'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { ScrollArea } from '@/components/ui/scroll-area'

export type FormComponent = React.ComponentType<{
  onSuccessfulSubmission: () => void
}>

type ContactDialogContextType = {
  openContactDialog: (FormComponent: FormComponent) => void
} | null

export const ContactDialogContext = createContext<ContactDialogContextType>(null)

export const useContactDialog = () => {
  const context = useContext(ContactDialogContext)
  if (!context) {
    throw new Error("'useContactDialog' must be used within a ContactDialogProvider's children.")
  }

  return context
}

type Props = {
  children: ReactNode
}

const ContactDialogProvider = ({ children }: Props) => {
  const [showForm, setShowForm] = useState(false)
  const [FormComponent, setFormComponent] = useState<FormComponent | null>(null)
  const [submitted, setSubmitted] = useState(false)

  const handleOpenChange = (open: boolean) => setShowForm(open)
  const handleClickContinue = () => setSubmitted(false)

  const handleSubmit = useCallback(() => {
    setShowForm(false)
    setSubmitted(true)
    triggerConfetti()
  }, [])

  const openContactDialog = (FormComponent: FormComponent) => {
    setFormComponent(() => FormComponent)
    setShowForm(true)
  }

  return (
    <ContactDialogContext.Provider value={{ openContactDialog }}>
      {children}
      <Dialog open={showForm} onOpenChange={handleOpenChange}>
        <DialogContent className="max-h-dvh">
          <ScrollArea className="max-h-[94dvh] pr-5 -mr-5">
            {FormComponent && <FormComponent onSuccessfulSubmission={handleSubmit} />}
          </ScrollArea>
        </DialogContent>
      </Dialog>
      <AlertDialog open={submitted}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Thank You!</AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div className="flex flex-col gap-2">
                <p>Thanks for reaching out! We’ll respond within 24 hours.</p>
                <p>We’re excited to help bring your dream to life.</p>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={handleClickContinue}>Continue</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </ContactDialogContext.Provider>
  )
}

const triggerConfetti = () => {
  const end = Date.now() + 3 * 1000 // 3 seconds
  const colors = ['#a786ff', '#fd8bbc', '#eca184', '#f8deb1']

  const frame = () => {
    if (Date.now() > end) return

    confetti({
      particleCount: 2,
      angle: 60,
      spread: 55,
      startVelocity: 60,
      origin: { x: 0, y: 0.5 },
      colors: colors,
    })
    confetti({
      particleCount: 2,
      angle: 120,
      spread: 55,
      startVelocity: 60,
      origin: { x: 1, y: 0.5 },
      colors: colors,
    })

    requestAnimationFrame(frame)
  }

  frame()
}

export default ContactDialogProvider
