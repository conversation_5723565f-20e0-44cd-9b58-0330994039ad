'use client'

import React from 'react'
import {
  ChallengeFormType,
  challengeOptions,
} from '@/app/components/forms/ChallengeForm/ChallengeSchema'
import { useFormContext } from 'react-hook-form'
import InputField from '@/app/components/form-fields/InputField'
import TextareaField from '@/app/components/form-fields/TextareaField'
import RadioGroupField from '@/app/components/form-fields/RadioGroupField'

const ChallengeFormFields = () => {
  const {
    control,
    watch,
    register,
    formState: { errors },
  } = useFormContext<ChallengeFormType>()
  const isOwnChallenge = watch('challenge') === 'own-challenge'

  return (
    <>
      <div className="space-y-4">
        <RadioGroupField
          name="challenge"
          options={challengeOptions}
          label="What's your top challenge?"
          control={control}
          error={errors.challenge}
        />
        <TextareaField
          name={'yourChallenge'}
          placeholder={
            isOwnChallenge
              ? 'Write what bugs you most on the road to making your dream a reality!'
              : "Select 'Your very own challenge' above to describe your specific challenge!"
          }
          register={register}
          disabled={!isOwnChallenge}
          className={!isOwnChallenge ? 'bg-muted cursor-not-allowed text-muted-foreground' : ''}
          error={errors.yourChallenge}
        />
      </div>
      <InputField
        name={'name'}
        label={'Your name:'}
        placeholder={'Your name'}
        register={register}
        error={errors.name}
      />
      <InputField
        name={'email'}
        label={'What email can we use for getting back to you?'}
        placeholder={'<EMAIL>'}
        description={"We'll never share your email with anyone else."}
        register={register}
        error={errors.email}
      />
    </>
  )
}

export default ChallengeFormFields
