import { AscensionLog } from '@/app/types/types'
import ListItemDescription from '@/app/components/list-items/ListItemDescription'
import ListItemHeading from '@/app/components/list-items/ListItemHeading'
import { memo } from 'react'

type Props = Pick<AscensionLog, 'title' | 'description'>

const AscensionLogListItem = ({ title, description }: Props) => (
  <>
    <ListItemHeading>{title}</ListItemHeading>
    <ListItemDescription>{description}</ListItemDescription>
  </>
)

export default memo(AscensionLogListItem)
