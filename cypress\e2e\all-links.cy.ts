describe('All Links Verification', () => {
  const ACCEPTED_STATUS_CODES = [200, 301, 302, 403, 404, 500]

  beforeEach(() => {
    cy.visit('http://localhost:3000')
  })

  describe('Service Accordion', () => {
    it('should navigate to services page when service link is clicked', () => {
      cy.get('.grid-in-service-accordion').contains('service page').click()
      cy.url().should('include', '/services')
      cy.go('back')
    })
  })

  describe('Ascension Logs', () => {
    it('should navigate to project pages when ascension log links are clicked', () => {
      cy.get('.grid-in-ascension-logs').contains('Recruiterly').click()
      cy.url().should('include', '/projects/recruiterly')
      cy.go('back')

      cy.get('.grid-in-ascension-logs').contains('Diobox').click()
      cy.url().should('include', '/projects/diobox')
      cy.go('back')
    })
  })

  describe('Service Carousel Cards', () => {
    it('should navigate correctly when service carousel links are clicked', () => {
      const selector = '.grid-in-service [data-slot="carousel-item"] a'
      cy.get(selector).each(($link, index) => {
        const href = $link.attr('href')

        cy.get(selector).eq(index).click()
        cy.url().should('include', href)
        cy.go('back')
      })
    })
  })

  describe('Client Feedback Carousel Cards', () => {
    it('should verify external links return acceptable status codes', () => {
      cy.get('.grid-in-feedback')
        .find('a')
        .each($link => {
          const url = $link.attr('href')
          const text = $link.text()

          if (!url || text.includes('See more!')) return

          cy.request(url).then(response => {
            expect(response.status).to.be.oneOf(ACCEPTED_STATUS_CODES)
          })
        })
    })

    it('should navigate to client feedback page when "See more" link is clicked', () => {
      cy.get('.grid-in-feedback').contains('See more!').click()
      cy.url().should('include', '/client-feedback')
      cy.go('back')
    })
  })

  describe('Project Carousel Cards', () => {
    it('should navigate correctly when project carousel links are clicked', () => {
      const selector = '.grid-in-project [data-slot="carousel-item"] a'
      cy.get(selector).each(($link, index) => {
        const href = $link.attr('href')

        cy.get(selector).eq(index).click()
        cy.url().should('include', href)
        cy.go('back')
      })
    })
  })

  describe('Footer Links', () => {
    it('should navigate correctly when footer internal links are clicked', () => {
      cy.get('footer').contains('manystack').click()
      cy.url().should('include', '/')

      cy.get('footer').contains('Our Projects').click()
      cy.url().should('include', '/projects')
      cy.go('back')

      cy.get('footer').contains('Our Services').click()
      cy.url().should('include', '/services')
      cy.go('back')

      cy.get('footer').contains('Privacy Promise').click()
      cy.url().should('include', '/policies/privacy-promise')
      cy.go('back')

      cy.get('footer').contains('Friendly Terms').click()
      cy.url().should('include', '/policies/friendly-terms')
      cy.go('back')
    })

    it('should verify footer social media links return acceptable status codes', () => {
      // expecting 400 as we don't have a facebook page
      cy.request({
        url: 'https://facebook.com/mnystck',
        failOnStatusCode: false,
      }).then(response => {
        expect(response.status).to.be.oneOf([...ACCEPTED_STATUS_CODES, 400])
      })

      cy.request({
        url: 'https://x.com/mnystck',
      }).then(response => {
        expect(response.status).to.be.oneOf(ACCEPTED_STATUS_CODES)
      })

      cy.request({
        url: 'https://www.linkedin.com/company/manystack',
      }).then(response => {
        expect(response.status).to.be.oneOf(ACCEPTED_STATUS_CODES)
      })
    })

    it('should verify footer email link has correct mailto attributes', () => {
      cy.get('footer').contains('Drop an email').should('exist')

      // Wait for email link to be built
      cy.wait(1000)

      cy.get('footer')
        .contains('Drop an email')
        .should('have.attr', 'href')
        .and('include', 'mailto:')
        .and('include', '<EMAIL>')
        .and('include', 'subject=My%20dream')
    })
  })

  describe('External Links', () => {
    it('should verify all external links have proper target and href attributes', () => {
      cy.get('a[href^="http"]').each($link => {
        const href = $link.attr('href')

        if (href && !href.includes('localhost')) {
          cy.wrap($link)
            .should('have.attr', 'target', '_blank')
            .should('have.attr', 'href')
            .and('match', /^https?:\/\//)
        }
      })
    })
  })
})
