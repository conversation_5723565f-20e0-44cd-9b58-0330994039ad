'use client'

import React from 'react'
import {
  CooperationFormType,
  projectTypeOptions,
} from '@/app/components/forms/CooperationForm/CooperationSchema'
import { useFormContext } from 'react-hook-form'
import InputField from '@/app/components/form-fields/InputField'
import TextareaField from '@/app/components/form-fields/TextareaField'
import RadioGroupField from '@/app/components/form-fields/RadioGroupField'

const CooperationFormFields = () => {
  const {
    control,
    register,
    formState: { errors },
  } = useFormContext<CooperationFormType>()

  return (
    <>
      <RadioGroupField
        name="projectType"
        options={projectTypeOptions}
        label="What kind of dream project are we going to work on?"
        control={control}
        error={errors.projectType}
      />
      <InputField
        name={'name'}
        label={'Your name:'}
        placeholder={'Your name'}
        register={register}
        error={errors.name}
      />
      <InputField
        name={'email'}
        label={'What email can we use for getting back to you?'}
        placeholder={'<EMAIL>'}
        description={"We'll never share your email with anyone else."}
        register={register}
        error={errors.email}
      />
      <TextareaField
        name={'message'}
        label={'Message'}
        placeholder={'Share any extra thoughts here!'}
        register={register}
        error={errors.message}
      />
    </>
  )
}

export default CooperationFormFields
