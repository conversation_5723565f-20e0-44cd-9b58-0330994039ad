// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Carousel Rendering & Snapshots should render basic carousel with multiple slides 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative"
    data-slot="carousel"
    role="region"
  >
    <div
      class="overflow-hidden"
      data-slot="carousel-content"
    >
      <div
        class="flex -ml-4"
        style="transform: translate3d(NaNpx,0px,0px);"
      >
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          1
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          2
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          3
        </div>
      </div>
    </div>
    <button
      class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 absolute size-8 rounded-full top-1/2 -left-12 -translate-y-1/2"
      data-slot="carousel-previous"
      disabled=""
    >
      <svg
        data-testid="chevron-left"
      />
      <span
        class="sr-only"
      >
        Previous slide
      </span>
    </button>
    <button
      class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 absolute size-8 rounded-full top-1/2 -right-12 -translate-y-1/2"
      data-slot="carousel-next"
      disabled=""
    >
      <svg
        data-testid="chevron-right"
      />
      <span
        class="sr-only"
      >
        Next slide
      </span>
    </button>
  </div>
</div>
`;

exports[`Carousel Rendering & Snapshots should render carousel with loop option enabled 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative"
    data-slot="carousel"
    role="region"
  >
    <div
      class="overflow-hidden"
      data-slot="carousel-content"
    >
      <div
        class="flex -ml-4"
        style="transform: translate3d(0px,0px,0px);"
      >
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          1
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          2
        </div>
      </div>
    </div>
    <button
      class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 absolute size-8 rounded-full top-1/2 -left-12 -translate-y-1/2"
      data-slot="carousel-previous"
    >
      <svg
        data-testid="chevron-left"
      />
      <span
        class="sr-only"
      >
        Previous slide
      </span>
    </button>
    <button
      class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 absolute size-8 rounded-full top-1/2 -right-12 -translate-y-1/2"
      data-slot="carousel-next"
    >
      <svg
        data-testid="chevron-right"
      />
      <span
        class="sr-only"
      >
        Next slide
      </span>
    </button>
  </div>
</div>
`;

exports[`Carousel Rendering & Snapshots should render carousel with navigation buttons 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative"
    data-slot="carousel"
    role="region"
  >
    <div
      class="overflow-hidden"
      data-slot="carousel-content"
    >
      <div
        class="flex -ml-4"
        style="transform: translate3d(NaNpx,0px,0px);"
      >
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          1
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          2
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          3
        </div>
      </div>
    </div>
    <button
      class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 absolute size-8 rounded-full top-1/2 -left-12 -translate-y-1/2"
      data-slot="carousel-previous"
      disabled=""
    >
      <svg
        data-testid="chevron-left"
      />
      <span
        class="sr-only"
      >
        Previous slide
      </span>
    </button>
    <button
      class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 absolute size-8 rounded-full top-1/2 -right-12 -translate-y-1/2"
      data-slot="carousel-next"
      disabled=""
    >
      <svg
        data-testid="chevron-right"
      />
      <span
        class="sr-only"
      >
        Next slide
      </span>
    </button>
  </div>
</div>
`;

exports[`Carousel Rendering & Snapshots should render large number of slides without crashing 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative"
    data-slot="carousel"
    role="region"
  >
    <div
      class="overflow-hidden"
      data-slot="carousel-content"
    >
      <div
        class="flex -ml-4"
        style="transform: translate3d(NaNpx,0px,0px);"
      >
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          1
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          2
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          3
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          4
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          5
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          6
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          7
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          8
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          9
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          10
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          11
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          12
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          13
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          14
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          15
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          16
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          17
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          18
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          19
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          20
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          21
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          22
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          23
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          24
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          25
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          26
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          27
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          28
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          29
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          30
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          31
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          32
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          33
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          34
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          35
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          36
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          37
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          38
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          39
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          40
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          41
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          42
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          43
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          44
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          45
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          46
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          47
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          48
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          49
        </div>
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          50
        </div>
      </div>
    </div>
    <button
      class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 absolute size-8 rounded-full top-1/2 -left-12 -translate-y-1/2"
      data-slot="carousel-previous"
      disabled=""
    >
      <svg
        data-testid="chevron-left"
      />
      <span
        class="sr-only"
      >
        Previous slide
      </span>
    </button>
    <button
      class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 absolute size-8 rounded-full top-1/2 -right-12 -translate-y-1/2"
      data-slot="carousel-next"
      disabled=""
    >
      <svg
        data-testid="chevron-right"
      />
      <span
        class="sr-only"
      >
        Next slide
      </span>
    </button>
  </div>
</div>
`;

exports[`Carousel Rendering & Snapshots should render with custom className 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative my-carousel"
    data-slot="carousel"
    role="region"
  >
    <div
      class="overflow-hidden"
      data-slot="carousel-content"
    >
      <div
        class="flex -ml-4"
        style="transform: translate3d(0px,0px,0px);"
      >
        <div
          aria-roledescription="slide"
          class="min-w-0 shrink-0 grow-0 basis-full pl-4"
          data-slot="carousel-item"
          role="group"
        >
          Slide
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Carousel Rendering & Snapshots should render with no slides 1`] = `
<div>
  <div
    aria-roledescription="carousel"
    class="relative"
    data-slot="carousel"
    role="region"
  >
    <div
      class="overflow-hidden"
      data-slot="carousel-content"
    >
      <div
        class="flex -ml-4"
        style="transform: translate3d(NaNpx,0px,0px);"
      />
    </div>
  </div>
</div>
`;
