name: CI/CD Pipeline

on:
  push:
    branches:
      - develop
  pull_request:
    branches:
      - develop

jobs:
  lint-test:
    name: ESLint
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'yarn'
      - run: yarn install --frozen-lockfile
      - run: yarn lint

  jest-tests:
    name: Jest Unit Tests
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'yarn'
      - run: yarn install --frozen-lockfile
      - run: yarn test

  typescript-check:
    name: TypeScript Type Check
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'yarn'
      - run: yarn install --frozen-lockfile
      - run: npx tsc --noEmit
      
  cypress-e2e:
    name: Cypress E2E Tests
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: cypress-io/github-action@v6
        with:
          build: yarn build
          start: yarn start
          wait-on: 'http://localhost:3000'
          wait-on-timeout: 120
