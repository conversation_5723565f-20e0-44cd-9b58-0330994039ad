'use client'

import { steps } from '@/app/data/steps'
import CarouselCard from '@/app/components/carousel-cards/CarouselCard'
import { useRandomCarouselScrollManager } from '@/app/hooks/useRandomCarouselScrollManager'
import ListItem from '@/app/components/list-items/ListItem'
import ServiceListItem from '@/app/components/list-items/types/ServiceListItem'

const PlanCarouselSection = () => {
  const { plugins, setApi, startAutoplay, stopAutoplay } = useRandomCarouselScrollManager([steps])

  return (
    <CarouselCard
      className="w-full h-fit"
      plugins={[plugins[0]]}
      setApi={setApi(0)}
      onMouseEnter={stopAutoplay}
      onMouseLeave={startAutoplay}
    >
      {steps.map(({ title, content }) => (
        <ListItem key={title}>
          <ServiceListItem title={title} description={content} />
        </ListItem>
      ))}
    </CarouselCard>
  )
}

export default PlanCarouselSection
