import { ReactNode } from 'react'
import Heading from '@/app/components/ui/Heading'
import PageSection from '@/app/components/ui/PageSection'

type Props = {
  title: string
  message: string
  CTAs?: ReactNode
}

const ErrorPage = ({ title, message, CTAs }: Props) => {
  return (
    <PageSection as="main" className="h-screen flex flex-col justify-center text-center gap-4">
      <Heading as="h1">{title}</Heading>
      <div className="flex flex-col items-center gap-8 text-gray-500">
        <p className="whitespace-pre-line">{message}</p>
        {CTAs && <div className="flex flex-col items-center gap-4 *:w-fit">{CTAs}</div>}
      </div>
    </PageSection>
  )
}

export default ErrorPage
