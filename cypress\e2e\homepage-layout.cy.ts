describe('Homepage Layout', () => {
  beforeEach(() => {
    cy.visit('http://localhost:3000')
  })

  describe('Content Section', () => {
    it('should render all content sections', () => {
      cy.get('.grid-in-hero').should('exist')
      cy.get('.grid-in-project').should('exist')
      cy.get('.grid-in-roadblocks').should('exist')
      cy.get('.grid-in-feedback').should('exist')
      cy.get('.grid-in-ascension-logs').should('exist')
      cy.get('.grid-in-application-development-intro').should('exist')
      cy.get('.grid-in-cards-plan').should('exist')
      cy.get('.grid-in-service-accordion').should('exist')
      cy.get('.grid-in-service').should('exist')
    })
  })
})
