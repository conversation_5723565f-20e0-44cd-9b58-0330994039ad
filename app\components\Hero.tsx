import Heading from '@/app/components/ui/Heading'
import ContactButton from '@/app/components/ContactButton'
import EmailSubscriptionForm from '@/app/components/forms/EmailSubscriptionForm/EmailSubscriptionForm'

const Hero = () => {
  return (
    <div className="flex flex-col justify-center text-center h-screen gap-8 -my-10">
      <Heading as="h1" className="font-extrabold text-4xl">
        Manystack App Development Company for Web & Mobile
      </Heading>
      <p className="text-4xl">manystack</p>
      <Heading>Our dream is to build your dream</Heading>
      <p>
        Your dream project deserves a homely spot to come alive. Join us in a playful alliance:
        <br />
        give way to an app that leaves each one of your users in awe.
      </p>
      <div className="flex flex-wrap justify-center gap-4">
        <ContactButton variant="rainbow" FormComponent={EmailSubscriptionForm}>
          Chart your concept!
        </ContactButton>
        <ContactButton variant="outline">Drop a line to start!</ContactButton>
      </div>
    </div>
  )
}

export default Hero
