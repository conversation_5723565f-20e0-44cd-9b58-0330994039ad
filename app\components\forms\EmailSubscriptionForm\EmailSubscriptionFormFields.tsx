'use client'

import React from 'react'
import { useFormContext } from 'react-hook-form'
import InputField from '@/app/components/form-fields/InputField'
import TextareaField from '@/app/components/form-fields/TextareaField'
import { EmailSubscriptionFormType } from '@/app/components/forms/EmailSubscriptionForm/EmailSubscriptionSchema'

const EmailSubscriptionFormFields = () => {
  const {
    register,
    formState: { errors },
  } = useFormContext<EmailSubscriptionFormType>()

  return (
    <>
      <InputField
        name={'name'}
        label={'Your name:'}
        placeholder={'Your name'}
        register={register}
        error={errors.name}
      />
      <InputField
        name={'email'}
        label={'What email can we use for getting back to you?'}
        placeholder={'<EMAIL>'}
        description={"We'll never share your email with anyone else."}
        register={register}
        error={errors.email}
      />
      <TextareaField
        name={'additionalNotes'}
        label={'Anything to add'}
        placeholder={'Share any extra thoughts here!'}
        register={register}
        error={errors.additionalNotes}
      />
    </>
  )
}

export default EmailSubscriptionFormFields
