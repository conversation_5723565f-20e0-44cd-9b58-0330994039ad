'use client'

import { useState, useEffect } from 'react'
import defaultTheme from 'tailwindcss/defaultTheme'
import { useWindowSize } from 'usehooks-ts'

const xlScreenWidth = parseInt(defaultTheme.screens.xl) * 16

export const useIsXlScreen = () => {
  const { width: windowWidth } = useWindowSize()
  const [isXlScreen, setIsXlScreen] = useState<boolean>()

  useEffect(() => {
    if (windowWidth !== undefined) {
      setIsXlScreen(windowWidth >= xlScreenWidth)
    }
  }, [windowWidth])

  return isXlScreen
}
