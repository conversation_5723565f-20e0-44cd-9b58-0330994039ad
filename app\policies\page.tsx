import { Metadata } from 'next'
import React from 'react'
import { getPagesByFolder } from '@/lib/getPagesByFolder'
import UnorderedList from '@/app/components/ui/UnorderedList'
import Header from '@/app/components/ui/Heading'
import NextLink from '@/app/components/ui/NextLink'

export const metadata: Metadata = {
  title: 'Policies',
}

const pages = getPagesByFolder('policies/(mdx-pages)')

const PoliciesPage = () => (
  <main className="flex flex-col justify-center gap-8">
    <Header as="h1" className="text-4xl">
      Policies
    </Header>
    <UnorderedList>
      {pages.map(({ title, slug }) => (
        <li key={title}>
          <NextLink href={`/policies/${slug}`}>{title}</NextLink>
        </li>
      ))}
    </UnorderedList>
  </main>
)

export default PoliciesPage
