'use client'

import { CSSProperties, ReactNode, JSX } from 'react'
import useDynamicLineClamp from '@/app/hooks/useDynamicLineClamp'
import { cn } from '@/lib/utils'
import { ClassValue } from 'clsx'

type Props = {
  children: ReactNode
  as?: JSX.ElementType
  className?: ClassValue
}

const LineClamp = ({ children, as: Tag = 'p', className }: Props) => {
  const { clampContainerRef, clampLines, maxHeight } = useDynamicLineClamp()

  const clampStyle: CSSProperties = {
    WebkitLineClamp: clampLines?.toString(),
    overflow: 'hidden',
    display: '-webkit-box',
    WebkitBoxOrient: 'vertical',
  }

  return (
    <div
      ref={clampContainerRef}
      className={cn('flex flex-col flex-1 min-h-0 hyphens-auto', className)}
      style={{ maxHeight: maxHeight ? `${maxHeight}px` : undefined }}
    >
      <Tag style={clampStyle}>{children}</Tag>
    </div>
  )
}

export default LineClamp
