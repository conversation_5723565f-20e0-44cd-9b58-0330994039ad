'use client'

import { orderBy } from 'lodash'
import CarouselCard from '@/app/components/carousel-cards/CarouselCard'
import ContactButton from '@/app/components/ContactButton'
import ClientFeedbackListItem from '@/app/components/list-items/types/ClientFeedbackListItem'
import OverlayLayout from '@/app/components/OverlayLayout'
import ListItemHeading from '@/app/components/list-items/ListItemHeading'
import ListItemDescription from '@/app/components/list-items/ListItemDescription'
import LineClamp from '@/app/components/LineClamp'
import { useAsideCarouselStore } from '@/app/store/asideCarousels.store'

const ClientFeedbackCarouselCard = () => {
  const {
    carouselStates: {
      feedbackCarousel: {
        carouselData: clientFeedback,
        plugins,
        setApi,
        stopAutoplay,
        startAutoplay,
      },
    },
  } = useAsideCarouselStore()

  // Don't render if clientFeedback is not available
  if (!clientFeedback || clientFeedback.length === 0) {
    return null
  }

  const featuredClientFeedback = clientFeedback.filter(({ featured }) => featured)
  const firstCard = (
    <div className="flex flex-col justify-center gap-4 mx-8">
      <LineClamp className="*:max-xl:!line-clamp-none" as={ListItemHeading}>
        Over nearly 20 years, we’ve had the joy of crafting online heartquarters for several
        dreambuilders—entrepreneurs whose visions blossomed into amazing apps.
      </LineClamp>
      <LineClamp className="*:max-xl:!line-clamp-none" as={ListItemDescription}>
        <p>
          Here’s how a few of our cloud-settling partners experienced dreamcrafting with Manystack→
        </p>
      </LineClamp>
    </div>
  )

  const lastCard = (
    <div className="flex flex-col justify-center gap-6 mx-8">
      <LineClamp className="*:max-xl:line-clamp-none" as={ListItemHeading}>
        Will you be the next cloudsettler, enjoying our playful alliance while building something
        remarkable for the future?
      </LineClamp>
      <ContactButton>Drop a line to start!</ContactButton>
    </div>
  )

  return (
    <CarouselCard
      title="Building Dreams The Manystack Way"
      className="w-full h-auto flex-1 [&>div]:pb-20"
      plugins={plugins}
      setApi={setApi}
      onMouseEnter={stopAutoplay}
      onMouseLeave={startAutoplay}
      overlay={<OverlayLayout cardOverlayLabel="See more!" link="/client-feedback" />}
    >
      {firstCard}
      {orderBy(featuredClientFeedback, 'order').map(({ id, text, note, featured, client }) => (
        <ClientFeedbackListItem
          key={id}
          text={text}
          note={note}
          client={client}
          featured={featured}
          truncate
        />
      ))}
      {lastCard}
    </CarouselCard>
  )
}

export default ClientFeedbackCarouselCard
