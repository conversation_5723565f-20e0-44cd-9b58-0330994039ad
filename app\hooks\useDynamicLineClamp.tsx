import { RefObject, useRef, useState } from 'react'
import { useResizeObserver } from 'usehooks-ts'

function useDynamicLineClamp() {
  const clampContainerRef = useRef<HTMLDivElement>(null)
  const [clampLines, setClampLines] = useState<number>()
  const [maxHeight, setMaxHeight] = useState<number>()

  const calculateLineClamp = () => {
    if (!clampContainerRef.current) return
    const element = clampContainerRef.current
    const computedStyle = window.getComputedStyle(element)
    const lineHeight = parseInt(computedStyle.lineHeight) || 24
    const height = Math.round(parseFloat(computedStyle.height)) || 0
    const lines = Math.floor(height / lineHeight)
    setClampLines(isNaN(lines) ? 0 : lines)
    const child = element.children[0]
    setMaxHeight(child.scrollHeight)
  }

  useResizeObserver({
    ref: clampContainerRef as RefObject<HTMLElement>,
    onResize: calculateLineClamp,
  })

  return { clampContainerRef, clampLines, maxHeight }
}

export default useDynamicLineClamp
