'use client'

import { useRandomCarouselScrollManager } from '@/app/hooks/useRandomCarouselScrollManager'
import { ascensionLogs } from '@/app/data/ascensionLogs'
import CarouselCard from '@/app/components/carousel-cards/CarouselCard'
import ListItem from '@/app/components/list-items/ListItem'
import AscensionLogListItem from '@/app/components/list-items/types/AscensionLogListItem'

const AscensionCarouselCard = () => {
  const { plugins, setApi, startAutoplay, stopAutoplay } = useRandomCarouselScrollManager([
    ascensionLogs,
  ])

  return (
    <CarouselCard
      className="w-full h-auto"
      plugins={[plugins[0]]}
      setApi={setApi(0)}
      onMouseEnter={stopAutoplay}
      onMouseLeave={startAutoplay}
    >
      {ascensionLogs.map(({ id, title, description }) => (
        <ListItem key={id}>
          <AscensionLogListItem title={title} description={description} />
        </ListItem>
      ))}
    </CarouselCard>
  )
}

export default AscensionCarouselCard
