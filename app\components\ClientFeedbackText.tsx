import { Feedback } from '@/app/types/types'
import FeedbackClampedTexts from '@/app/components/FeedbackClampedTexts'

type Props = Pick<Feedback, 'text' | 'note'> & { truncate?: boolean }

const ClientFeedbackText = ({ text, note, truncate }: Props) => {
  if (!truncate) {
    return (
      <>
        <blockquote className="text-gray-700">{text}</blockquote>
        {note && <p className="italic text-gray-600">{note}</p>}
      </>
    )
  }

  return <FeedbackClampedTexts text={text} note={note} />
}

export default ClientFeedbackText
