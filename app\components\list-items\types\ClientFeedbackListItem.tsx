import { Feedback } from '@/app/types/types'
import { cn } from '@/lib/utils'
import { memo } from 'react'
import ClientFeedbackText from '@/app/components/ClientFeedbackText'

type Props = Pick<Feedback, 'text' | 'note' | 'featured' | 'client'> & { truncate?: boolean }

const ClientFeedbackListItem = ({
  text,
  note,
  featured,
  client: { name, title, company, website },
  truncate = false,
}: Props) => (
  <div className="grid grid-cols-[auto_1fr] gap-4 h-full content-center">
    <span className="-mt-2 text-8xl text-gray-400">”</span>
    <div className={cn('flex flex-col overflow-hidden mr-8', featured ? 'gap-4' : 'gap-8')}>
      <ClientFeedbackText truncate={truncate} note={note} text={text} />
      <div className="text-gray-600 *:line-clamp-1 flex-shrink-0" rel="author">
        <p className="font-semibold">
          {name}
          {title && (
            <>
              {' '}
              - <span>{title}</span>
            </>
          )}
        </p>
        {company && <span className="block">{company}</span>}
        {website && (
          <a className="block underline truncate" href={website} target="_blank">
            {website}
          </a>
        )}
      </div>
    </div>
  </div>
)

export default memo(ClientFeedbackListItem)
