'use client'

import {
  Accordion as AccordionContainer,
  AccordionContent,
  AccordionTrigger,
  AccordionItem,
} from '@/components/ui/accordion'
import { useIsXlScreen } from '@/app/hooks/useIsXlScreen'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useCallback, useEffect, useRef, useState, RefObject } from 'react'
import { min } from 'lodash'
import { useResizeObserver } from 'usehooks-ts'
import { getInnerWidth } from '@/lib/getInnerWidth'
import { cn } from '@/lib/utils'
import NextLink from '@/app/components/ui/NextLink'
import Heading from '@/app/components/ui/Heading'

type Props = {
  items: { id: string; title: string; text: string; path?: string }[]
}

const Accordion = ({ items = [] }: Props) => {
  const isXlScreen = useIsXlScreen()
  const accordionRef = useRef<HTMLDivElement>(null)
  const itemRefs = useRef<(HTMLDivElement | null)[]>([])
  const itemWidthRef = useRef<number>(null)
  const [scrollAreaWidth, setScrollAreaWidth] = useState<number>()

  const handleResize = useCallback(() => {
    if (!itemWidthRef.current) return
    const accordionWidth = getInnerWidth(accordionRef.current)
    const contentWidth = accordionWidth - itemWidthRef.current * items.length
    setScrollAreaWidth(contentWidth)
  }, [items.length])

  useEffect(() => {
    if (!isXlScreen || !itemRefs.current[0] || itemWidthRef.current) return
    itemWidthRef.current = min(itemRefs.current.map(ref => ref?.getBoundingClientRect().width)) || 0
  }, [isXlScreen])

  useResizeObserver({
    ref: accordionRef as RefObject<HTMLElement>,
    onResize: () => {
      if (!isXlScreen) return
      handleResize()
    },
  })

  if (!items.length) return null

  return (
    <AccordionContainer
      type="single"
      defaultValue={items[0].id}
      orientation={isXlScreen ? 'horizontal' : 'vertical'}
      className={cn(
        'grid grid-flow-row xl:grid-flow-col',
        'overflow-hidden',
        'border border-gray-300 rounded-md bg-gray-200'
      )}
      ref={accordionRef}
    >
      {items.map(({ id, title, text, path }, index) => (
        <AccordionItem
          key={id}
          value={id}
          ref={ref => {
            itemRefs.current[index] = ref
          }}
          className="grid grid-flow-row xl:grid-flow-col"
        >
          <AccordionTrigger
            className={cn(
              'bg-gray-300',
              'min-w-7 py-2 px-1 gap-2',
              'xl:[writing-mode:sideways-lr] xl:justify-end'
            )}
          >
            <Heading as="h3" className="text-sm">
              {title}
            </Heading>
          </AccordionTrigger>
          <AccordionContent className="xl:h-96 p-0">
            <ScrollArea
              className="h-full"
              thumbClassName="bg-gray-300"
              style={{ width: isXlScreen && scrollAreaWidth ? scrollAreaWidth : '100%' }}
            >
              <p className="whitespace-pre-wrap p-2">
                {text} {path && <NextLink href={path}>Learn more</NextLink>}
              </p>
            </ScrollArea>
          </AccordionContent>
        </AccordionItem>
      ))}
    </AccordionContainer>
  )
}

export default Accordion
