import { FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Textarea } from '@/components/ui/textarea'
import { FieldError, FieldValues, Path, UseFormRegister } from 'react-hook-form'

type Props<TFieldValues extends FieldValues> = {
  register: UseFormRegister<TFieldValues>
  name: Path<TFieldValues>
  label?: string
  placeholder?: string
  error?: FieldError
  disabled?: boolean
  className?: string
}

const TextareaField = <TFieldValues extends FieldValues>({
  register,
  name,
  label,
  placeholder,
  error,
  disabled,
  className,
}: Props<TFieldValues>) => {
  return (
    <FormItem>
      {label && <FormLabel htmlFor={name}>{label}</FormLabel>}
      <Textarea
        disabled={disabled}
        {...register(name)}
        placeholder={placeholder}
        rows={5}
        className={className}
      />
      {!disabled && <FormMessage>{error?.message?.toString()}</FormMessage>}
    </FormItem>
  )
}

export default TextareaField
