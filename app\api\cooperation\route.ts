import { CooperationSchema } from '@/app/components/forms/CooperationForm/CooperationSchema'
import { processEmailRequest } from '@/lib/email'

export async function POST(req: Request) {
  return processEmailRequest<typeof CooperationSchema>({
    req,
    schema: CooperationSchema,
    emailConfig: {
      subject: 'Cooperation Inquiry from manystack.com',
      formType: 'Cooperation request',
      formatBody: data =>
        `Name: ${data.name}

        Email: ${data.email}

        Project Type: ${data.projectType}

        Message: ${data.message}

          
        This user is interested in cooperation.`,
    },
  })
}
