import { cn } from '@/lib/utils'
import { ReactNode } from 'react'

const AsideWrapper = ({ children, className }: { children: ReactNode; className?: string }) => (
  <aside
    className={cn(
      'flex xl:!max-w-[min(25vw,theme(containers.sm))] xl:tall:!max-w-[min(25vw,theme(containers.md))] xl:pt-0 xl:border-b-0',
      className
    )}
  >
    {children}
  </aside>
)

export default AsideWrapper
