import Image from 'next/image'

type Props = {
  thumbnail?: string
  title: string
}

const ListItemThumbnail = ({ thumbnail, title }: Props) => {
  if (!thumbnail) return null

  return (
    <div className="bg-white flex shrink justify-center w-full rounded overflow-hidden">
      <Image
        width="310"
        height="310"
        className="size-full object-contain"
        src={`/images/${thumbnail}`}
        alt={title}
      />
    </div>
  )
}

export default ListItemThumbnail
