/// <reference types="cypress" />
/* eslint-disable @typescript-eslint/no-namespace */

/// <reference types="cypress" />

Cypress.Commands.add('openForm', (formName: string, dialogText: string) => {
  cy.contains(formName).click()
  cy.get('[role="dialog"]').should('be.visible')
  cy.get('[role="dialog"]').should('contain.text', dialogText)
})

Cypress.Commands.add('fillInput', (name: string, value: string) => {
  cy.get(`input[name="${name}"]`).type(value)
})

Cypress.Commands.add('fillTextarea', (name: string, value: string) => {
  cy.get(`textarea[name="${name}"]`).type(value)
})

Cypress.Commands.add('checkCheckbox', () => {
  cy.get('[role="checkbox"]').click()
})

Cypress.Commands.add('selectRadioOption', (value: string) => {
  cy.get(`button[role="radio"][value="${value}"]`).click()
})

Cypress.Commands.add('isRadioChecked', (value: string) => {
  cy.get(`button[role="radio"][value="${value}"]`).should('have.attr', 'aria-checked', 'true')
})

Cypress.Commands.add('isTextareaEnabled', (name: string, enabled: boolean = true) => {
  const assertion = enabled ? 'be.enabled' : 'be.disabled'
  cy.get(`textarea[name="${name}"]`).should(assertion)
})

Cypress.Commands.add('submitForm', () => {
  cy.get('button[type="submit"]').click()
})

Cypress.Commands.add('expectValidationMessages', (count: number) => {
  cy.get('[id$="-form-item-message"]').should('exist')
  cy.get('[id$="-form-item-message"]').should('have.length', count)
})

Cypress.Commands.add('expectThankYouScreen', () => {
  cy.contains('Thank You!').should('be.visible')
  cy.contains('Continue').click()
  cy.contains('Thank You!').should('not.exist')
})

declare global {
  namespace Cypress {
    interface Chainable {
      openForm(formName: string, dialogText: string): Chainable<void>
      fillInput(name: string, value: string): Chainable<void>
      fillTextarea(name: string, value: string): Chainable<void>
      checkCheckbox(): Chainable<void>
      selectRadioOption(value: string): Chainable<void>
      isRadioChecked(value: string): Chainable<void>
      isTextareaEnabled(name: string, enabled: boolean): Chainable<void>
      submitForm(): Chainable<void>
      expectValidationMessages(count: number): Chainable<void>
      expectThankYouScreen(): Chainable<void>
    }
  }
}

export {}
