import { Service } from '@/app/types/types'
import ListItemDescription from '@/app/components/list-items/ListItemDescription'
import ListItemHeading from '@/app/components/list-items/ListItemHeading'
import LineClamp from '@/app/components/LineClamp'
import { memo } from 'react'

type Props = Pick<Service, 'title' | 'description'>

const ServiceListItem = ({ title, description }: Props) => (
  <>
    <ListItemHeading>{title}</ListItemHeading>
    <LineClamp className="*:max-xl:!line-clamp-none" as={ListItemDescription}>
      <p>{description}</p>
    </LineClamp>
  </>
)

export default memo(ServiceListItem)
