import ProjectIllustration from "@/app/components/ProjectIllustration"
import EmailSubscriptionForm from '@/app/components/forms/EmailSubscriptionForm/EmailSubscriptionForm'

export const metadata = {
  title: "Diobox – Reimagining Event Tech from the Ground Up",
  description: "<PERSON>obox teamed up with our cloudcraftsmen to ditch legacy systems and wow users with features like drag-and-drop seating and visual email builders. From slow to soaring—all thanks to smart tools and genuine care.",
  openGraph: {
    description: 'How <PERSON>obox soared past outdated tools with dreamcrafted upgrades.',
  },
}

# **Breathing New Life into an Event Management Heartquarters**

## **An Ascension Log of the Diobox Dream**

---

### **Time to Transform**

Events were booming, but New York-based Diobox, a seed company founded by <PERSON>, felt tied down by outdated tech. Yet, its core yearned to sparkle and engage even more users.

To turn those dreams into action, Diobox needed more than a snappy interface. They required user-friendly, time-saving features like:

- Visual email builder
- Drag-and-drop guest seating
- Customizable form builder
- Ticketing and guest check-in

So, they sought out a team that grasped legacy systems and could elevate new ideas.

---

### **Dreamcraftsmen to the Rescue**

This is when <PERSON><PERSON><PERSON> stepped in. We’re known for clever solutions and a genuine touch. By listening closely, we unearthed <PERSON><PERSON><PERSON>’s needs.

Realizing an upgrade’s seamlessness was crucial, we pieced together the perfect Tech Stack Balloon:

- Netlify for robust hosting
- CircleCI for speedy build processes
- Cypress for flawless user interactions
- Jest & Vitest for feature assurance
- Vite for an energized frontend
- Chakra UI for a sleek user interface

Jointly, we transformed the frontend and enriched the features step by step.

---

### **Heartquarters Revitalized**

After months of dedicated restoration and building, Diobox emerged with up-to-the-minute tools and features that delighted users:

#### **Fast & Fluid Frontend**
*We moved from Angular to a dynamic React setup. The new frontend is swift, responsive, and easy to manage.*

#### **Intuitive Email Builder**
<ProjectIllustration src="/illustrations/diobox/email-builder.gif" title="Visual Email Builder" />
*For composing powerful emails with drag-and-drop ease. Event planners now design and dispatch communications speedily.*

#### **Drag-and-Drop Guest Seating:**
<ProjectIllustration src="/illustrations/diobox/seating.gif" title="Drag-and-Drop Guest Seating" />
*For Arranging seating as easy as breeze. Visual tools make setup smooth and effortless.*

#### **Customizable Form Builder:**
<ProjectIllustration src="/illustrations/diobox/form-builder.gif" title="Customizable Form Builder" />
*Flexible forms for RSVPs and surveys. Tailor each form to specific needs.*

#### **Guest Check-In and Ticketing:**
<ProjectIllustration src="/illustrations/diobox/guest-check-in-ticketing.gif" title="Guest Check-In and Ticketing" />
*Streamlined check-ins and ticketing that reduces wait times to virtually zero.*

With new wings, Diobox took flight, becoming a true cloudsettler and a symbol of excellence in event management.

---

### **Reaching New Heights Together**

The alliance between Diobox and Manystack remains robust, always sparking new ideas. With eyes to the future, our ascension into the cloud continues.

Diobox’s story shows the magic in teamwork and tech flair. Paired with Manystack, it inspires dreambuilders to conceptualize and launch their own incredible platforms.

We dare you to <ContactButton variant="link" FormComponent={EmailSubscriptionForm}>chart your concept</ContactButton> or when ready for building, <ContactButton variant="link">drop a line to start!</ContactButton>
