'use client'

import React, { RefObject, useRef } from 'react'
import { useResizeObserver } from 'usehooks-ts'
import LineClamp from '@/app/components/LineClamp'
import { Feedback } from '@/app/types/types'

type Props = Pick<Feedback, 'text' | 'note'>

const FeedbackClampedTexts = ({ text, note }: Props) => {
  const feedbackRef = useRef<HTMLDivElement>(null)

  const calculateRefMaxHeight = (ref: HTMLDivElement) => {
    if (!ref) return 0
    const childrenMaxHeights = Array.from(ref.children).reduce(
      (acc, child) => acc + parseFloat(getComputedStyle(child).maxHeight),
      0
    )
    const rowGap = parseFloat(getComputedStyle(ref).rowGap)
    return childrenMaxHeights + rowGap
  }

  useResizeObserver({
    ref: feedbackRef as RefObject<HTMLElement>,
    onResize: () => {
      if (!feedbackRef.current) return
      const height = calculateRefMaxHeight(feedbackRef.current)
      feedbackRef.current.style.height = `${height}px`
    },
  })

  return (
    <div className="flex flex-col min-h-0 gap-4 max-xl:!h-fit" ref={feedbackRef}>
      <LineClamp className="*:max-xl:!line-clamp-none text-gray-700" as="blockquote">
        {text}
      </LineClamp>
      {note && (
        <LineClamp className="*:max-xl:!line-clamp-none italic text-gray-600">{note}</LineClamp>
      )}
    </div>
  )
}

export default FeedbackClampedTexts
