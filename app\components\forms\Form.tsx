'use client'
import React, { ReactNode } from 'react'
import { Separator } from '@/components/ui/separator'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Form as FormUI,
} from '@/components/ui/form'
import { Path, useForm, SubmitHandler, Resolver, DefaultValues } from 'react-hook-form'
import { Button } from '@/components/ui/button'
import { Loader2 } from 'lucide-react'
import { Checkbox } from '@/components/ui/checkbox'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { cn } from '@/lib/utils'

interface Props<T extends z.ZodObject> {
  validationSchema: T
  defaultValues: z.infer<T>
  children: ReactNode
  onSubmit: (values: z.infer<T>) => Promise<void>
  header?: ReactNode
  submitButtonText?: string
}

const Form = <T extends z.ZodObject>({
  validationSchema,
  defaultValues,
  header,
  children,
  onSubmit,
  submitButtonText = 'Submit',
}: Props<T>) => {
  type FormType = z.infer<typeof validationSchema>

  const form = useForm<FormType>({
    resolver: zodResolver(validationSchema) as Resolver<FormType>,
    defaultValues: defaultValues as DefaultValues<FormType>,
  })

  const {
    control,
    handleSubmit,
    clearErrors,
    reset,
    formState: { isSubmitting, defaultValues: formDefaultValues },
  } = form

  const hasAgreement = formDefaultValues && 'agreement' in formDefaultValues

  const handleFormSubmit: SubmitHandler<FormType> = async values => {
    await onSubmit(values)
    clearErrors()
    reset()
  }

  return (
    <>
      {header}
      {header && <Separator className="my-4" />}
      <FormUI {...form}>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-8 m-0.5">
          {children}
          {hasAgreement && (
            <FormField
              control={control}
              name={'agreement' as Path<FormType>}
              render={({ field }) => (
                <FormItem
                  className={cn(
                    'flex flex-row items-start',
                    'space-x-3 space-y-0 p-4',
                    'rounded-md border shadow'
                  )}
                >
                  <FormControl>
                    <Checkbox checked={!!field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                  <div className="space-y-4 leading-none">
                    <FormLabel className="!text-muted-foreground">
                      I appreciate that you only send me what matters and never share my info. I can
                      change my mind about access anytime.
                    </FormLabel>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="animate-spin" />}
            {submitButtonText}
          </Button>
        </form>
      </FormUI>
    </>
  )
}

export default Form
