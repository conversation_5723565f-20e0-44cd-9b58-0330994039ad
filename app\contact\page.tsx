import { Metadata } from 'next'
import Heading from '@/app/components/ui/Heading'
import ContactButton from '@/app/components/ContactButton'
import EmailSubscriptionForm from '@/app/components/forms/EmailSubscriptionForm/EmailSubscriptionForm'
import UnorderedList from '@/app/components/ui/UnorderedList'
import NextLink from '@/app/components/ui/NextLink'
import Blockquote from '@/app/components/ui/Blockquote'
import Table from '@/app/components/ui/Table'
import TBody from '@/app/components/ui/TBody'
import THead from '@/app/components/ui/THead'
import PageSection from '@/app/components/ui/PageSection'

export const metadata: Metadata = {
  title: 'Reach out to start building your dream app',
  description:
    "Say hello, share your idea, or send a sketch—Manystack makes it easy to start building your dream app. Whether you're ready to begin or just exploring, we're here to listen, collaborate, and build something amazing.",
  openGraph: {
    description: 'Ready when you are—reach out and let’s build.',
  },
}

const ContactPage = () => (
  <PageSection
    as="main"
    className="*:not-last:pb-12 space-y-12 divide-y-2 divide-bg-border [&_a]:underline"
  >
    <section className="flex flex-col gap-8">
      <Heading as="h1" className="text-4xl">
        Let’s start Crafting Something Amazing.
      </Heading>
      <div>
        <p>Your dream’s waiting to be built — and we’re ready when you are.</p>
        <p>Whether it’s big, small, half-baked, or napkin-scrawled, we want to hear it.</p>
      </div>
    </section>
    <section className="flex flex-col gap-8">
      <Heading>💬 Get in touch</Heading>
      <p>Your message, your way:</p>
      <UnorderedList>
        <li>
          <ContactButton variant="link">💬 Drop a line to start!</ContactButton>
        </li>
        <li>
          <ContactButton variant="link" FormComponent={EmailSubscriptionForm}>
            🗺️ Chart your concept!
          </ContactButton>
        </li>
      </UnorderedList>
    </section>
    <section className="flex flex-col gap-8">
      <Heading>✉️ Prefer a direct route?</Heading>
      <p>Feel free to write us directly. We read everything and respond with care.</p>
      <Table>
        <THead>
          <tr>
            <th>If you want to...</th>
            <th>Email</th>
          </tr>
        </THead>
        <TBody>
          <tr>
            <td>Build something amazing</td>
            <td>
              <NextLink href="mailto:<EMAIL>"><EMAIL></NextLink>
            </td>
          </tr>
          <tr>
            <td>Talk to the founder</td>
            <td>
              <NextLink href="mailto:<EMAIL>"><EMAIL></NextLink>
            </td>
          </tr>
          <tr>
            <td>Explore partnerships or ways to spread the word</td>
            <td>
              <NextLink href="mailto:<EMAIL>"><EMAIL></NextLink>
            </td>
          </tr>
          <tr>
            <td>Ask about anything else</td>
            <td>
              <NextLink href="mailto:<EMAIL>"><EMAIL></NextLink>
            </td>
          </tr>
        </TBody>
      </Table>
    </section>
    <section className="flex flex-col gap-8">
      <Heading>🌐 Find us around the cloud</Heading>
      <p>Let’s connect, share ideas, or swap memes. We’re humans behind the screens.</p>
      <UnorderedList>
        <li>
          <NextLink href="https://facebook.com/mnystck">Facebook</NextLink>
        </li>
        <li>
          <NextLink href="https://x.com/mnystck">X (Twitter)</NextLink>
        </li>
        <li>
          <NextLink href="https://linkedin.com/company/manystack">LinkedIn</NextLink>
        </li>
      </UnorderedList>
    </section>
    <aside>
      <Blockquote>
        <span>🕯️ Our dream is to build your dream.</span>
        <span className="block">
          So go ahead — tap that button, send that sketch, write that note.
        </span>
        <span>We’re listening.</span>
      </Blockquote>
    </aside>
  </PageSection>
)

export default ContactPage
